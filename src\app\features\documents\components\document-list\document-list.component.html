<div class="document-list-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>{{ 'DOCUMENTS.LOADING' | translate }}</p>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && documents.length === 0" class="empty-state">
    <div class="empty-icon">
      <mat-icon>folder_open</mat-icon>
    </div>
    <h3>{{ 'DOCUMENTS.NO_DOCUMENTS' | translate }}</h3>
    <p>{{ 'DOCUMENTS.NO_DOCUMENTS_MESSAGE' | translate }}</p>
  </div>

  <!-- Document Table -->
  <div *ngIf="!isLoading && documents.length > 0" class="documents-table">
    <app-table
      [columns]="tableColumns"
      [displayedColumns]="displayedColumns"
      [dataSource]="tableDataSource"
      [totalItems]="totalCount"
      [pageSize]="pageSize"
      (onClickAction)="onTableAction($event)"
      (pageChange)="onPageChange($event)">
    </app-table>
  </div>
</div>
