import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
// import { catchError, finalize } from 'rxjs/operators';
// import { of } from 'rxjs';

// Shared components
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Custom validators
import { CustomValidators } from '@shared/services/custom-validators.service';

// Core interfaces and enums
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { AppearanceEnum } from '@shared/enum/appearance-enum';
import { SizeEnum } from '@core/enums/size';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';

// User interfaces
import { IUser } from '../interfaces/user.interface';

// API
import {
  UserManagementServiceProxy,
  AddUserCommand,
  AuthorzationServiceProxy,
} from '@core/api/api.generated';

// Validators
import {
  saudiIbanValidator,
  saudiMobileValidator,
  saudiPassportValidator,
} from '@shared/validators/saudi-validators';
import { ErrorModalService } from '@core/services/error-modal.service';
import { catchError, of } from 'rxjs';
import { UserManagementService } from '@shared/services/users/user-management.service';
import Swal from 'sweetalert2';
import { AttachmentModule } from '@shared/enum/AttachmentModule';

// Validators - using built-in pattern validator for Saudi phone numbers

@Component({
  selector: 'app-create-user',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent,
    PageHeaderComponent,
    BreadcrumbComponent,
    CustomButtonComponent,
  ],
  providers: [UserManagementService],
  templateUrl: './create-user.component.html',
  styleUrls: ['./create-user.component.scss'],
})
export class CreateUserComponent implements OnInit {
  createUserForm!: FormGroup;
  formControls: IControlOption[] = [];
  isFormSubmitted = false;
  isLoading = false;
  pendingRoleReplacements: { roleName: string; existingUserId: number }[] = [];
  uniqueRoles: string[] = [];
  // Enums for template
  readonly ButtonTypeEnum = ButtonTypeEnum;

  breadcrumbItems: IBreadcrumbItem[] = [
    {
      label: 'BREADCRUMB.HOME',
      url: '/admin/dashboard',
    },
    {
      label: 'BREADCRUMB.USER_MANAGEMENT',
      url: '/admin/user-management',
    },
    {
      label: 'USER_MANAGEMENT.BREADCRUMB.CREATE_USER',
      url: '',
      active: true,
    },
  ];
  sigleRolesAvilavilty: any;
  fullName: any;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private userManagementService: UserManagementService,
    private errorModalService: ErrorModalService,
    private translateService: TranslateService,
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormControls();
    this.loadRoles();
    this.CheckRoleAvailability();
    this.uniqueRoles = [
      'legalcouncil',
      'financecontroller',
      'compliancelegalmanagingdirector',
      'headofrealestate',
    ];
  }
  CheckRoleAvailability() {
    this.userManagementService.CheckRoleAvailability().subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.sigleRolesAvilavilty = response.data;
          console.log('Role availability loaded:', this.sigleRolesAvilavilty);
        }
      },
      error: (error) => {
        console.error('Error loading role availability:', error);
        // Set default values if API fails
        this.sigleRolesAvilavilty = {
          legalCouncilHasActiveUser: false,
          financeControllerHasActiveUser: false,
          complianceLegalManagingDirectorHasActiveUser: false,
          headOfRealEstateHasActiveUser: false,
        };
      },
    });
  }

  private initializeForm(): void {
    this.createUserForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(255)]],
      email: ['', [Validators.required, CustomValidators.strictEmail, Validators.maxLength(255)]],
      mobile: ['', [Validators.required ]], //saudiMobileValidator() 11 digits starting with 5 (Saudi phone format)
      iban: ['', [saudiIbanValidator()]], // Optional field with Saudi IBAN validation
      nationality: ['', [Validators.maxLength(100)]], // Optional field, no default value
      cv: [''],
      personalPhoto: [''],
      passportNo: ['', [ saudiPassportValidator()]],
      roles: [[], [Validators.required]], // Changed to array to support multiple roles
      registrationMessageSent: [true],
      registrationCompleted: [false],
    });
  }

  private loadRoles(): void {
    this.userManagementService.getRolesList().subscribe({
      next: (response: any) => {
        if (response && response.data) {
          const rolesControl = this.formControls.find(
            (control) => control.formControlName === 'roles'
          );

          if (rolesControl) {
            // Map API response to dropdown options with proper id, name, and value properties
            rolesControl.options = response.data.map((role: any) => ({
              id: role.roleName,
              name: role.displayName,
              value: role.displayName,
            }));

            console.log('Roles loaded successfully:', rolesControl.options);
          }
        }
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.errorModalService.showError(
          'USER_MANAGEMENT.CREATE.ROLES_LOAD_ERROR'
        );
      },
    });
  }

  private setupFormControls(): void {
    this.formControls = [
      // Basic Information Section
      {
        formControlName: 'name',
        type: InputType.Text,
        id: 'name',
        name: 'name',
        label: 'USER_MANAGEMENT.CREATE.NAME',
        placeholder: 'USER_MANAGEMENT.CREATE.NAME_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-4',
        maxLength: 255,
      },
      {
        formControlName: 'email',
        type: InputType.Email,
        id: 'email',
        name: 'email',
        label: 'USER_MANAGEMENT.CREATE.EMAIL',
        placeholder: 'USER_MANAGEMENT.CREATE.EMAIL_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-4',
        maxLength: 255,
      },
      {
        formControlName: 'mobile',
        type: InputType.Tel,
        id: 'mobile',
        name: 'mobile',
        label: 'USER_MANAGEMENT.CREATE.PHONE_NUMBER',
        placeholder: 'USER_MANAGEMENT.CREATE.MOBILE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-4',
        maxLength: 10, // 10 digits for Saudi mobile format
        //pattern: '^(05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$', // Saudi mobile pattern
      },

      // Personal Details
      {
        formControlName: 'nationality',
        type: InputType.Text,
        id: 'nationality',
        name: 'nationality',
        label: 'USER_MANAGEMENT.CREATE.NATIONALITY',
        placeholder: 'USER_MANAGEMENT.CREATE.NATIONALITY_PLACEHOLDER',
        isRequired: false, // Optional field according to User Story 1223
        class: 'col-md-4',
        maxLength: 100, // Max 100 characters as per User Story 1223
      },
      {
        formControlName: 'passportNo',
        type: InputType.Mixed,
        id: 'passportNo',
        name: 'passportNo',
        label: 'USER_MANAGEMENT.CREATE.PASSPORT_NO',
        placeholder: 'USER_MANAGEMENT.CREATE.PASSPORT_NO_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-4',
        maxLength: 20, // 1 letter + 8 digits
        pattern: '^[A-Z][0-9]{8}$', // Pattern hint for UI
      },
      {
        formControlName: 'iban',
        type: InputType.Mixed,
        id: 'iban',
        name: 'iban',
        label: 'USER_MANAGEMENT.CREATE.IBAN',
        placeholder: 'USER_MANAGEMENT.CREATE.IBAN_PLACEHOLDER',
        isRequired: false, // Optional field according to User Story 1223
        class: 'col-md-4',
        maxLength: 24, // SA + 22 digits
        pattern: '^SA[0-9]{22}$', // Pattern hint for UI
      },

      // System Access & Role Information
      {
        formControlName: 'roles',
        type: InputType.Dropdown,
        id: 'roles',
        name: 'roles',
        label: 'USER_MANAGEMENT.CREATE.ROLE',
        placeholder: 'USER_MANAGEMENT.CREATE.ROLE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-4',
        multiple: true, // Start with single-select, will be dynamically updated
        options: [],
        maxLength: 3,
      },
      {
        formControlName: '',
        type: InputType.empty,
        id: '',
        name: '',
        label: '',
        placeholder: '',
        class: 'col-md-4',
      },
      {
        formControlName: 'cv',
        type: InputType.file,
        id: 'cv',
        name: 'cv',
        label: 'USER_MANAGEMENT.CREATE.CV',
        placeholder: 'USER_MANAGEMENT.CREATE.CV_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-12',
        allowedTypes: ['pdf', 'doc', 'docx'],
        max: 10, // 10MB max for CV
        moduleId : AttachmentModule.User
      },
      // {
      //   formControlName: 'personalPhoto',
      //   type: InputType.file,
      //   id: 'personalPhoto',
      //   name: 'personalPhoto',
      //   label: 'USER_MANAGEMENT.CREATE.PERSONAL_PHOTO',
      //   placeholder: 'USER_MANAGEMENT.CREATE.PERSONAL_PHOTO_PLACEHOLDER',
      //   isRequired: false,
      //   class: 'col-md-6',
      //   allowedTypes: ['jpg', 'jpeg', 'png'],
      //   max: 2, // 2MB max for photo
      // },
    ];
  }

  onValueChange(event: any, control: IControlOption): void {
    console.log('Value changed:', control.formControlName, event);
  }

  onKeyPressed(event: any, control: IControlOption): void {
    // Handle specific key press events if needed
  }
  onFileUploaded(data: any) {
    this.createUserForm
      .get(data.control.formControlName)
      ?.setValue(data.file.id);
  }
  onRemoveSelectedItem(data: any): void {
    console.log('Remove selected item:', data);
    if (data.control.formControlName == 'roles') {
      const current =
        this.createUserForm.get(data.control.formControlName)?.value || [];
      const updated = current.filter((id: any) => id !== data.idToRemove);
      this.createUserForm.get(data.control.formControlName)?.setValue(updated);
    }
  }
  onDropdownChange(event: any, control: IControlOption): void {
    // Handle role selection logic
    if (control.formControlName === 'roles') {
      this.handleRoleChange(event);
    }
  }

  /**
   * Enhanced role selection logic based on business requirements
   * Handles multi-select enablement and role conflict detection
   */
  private handleRoleChange(selectedRoles: string[]): void {
    console.log('Role change detected:', selectedRoles);
    if (!selectedRoles || !Array.isArray(selectedRoles)) {
      return;
    }


    // Get the roles control from formControls
    const rolesControl = this.formControls.find(
      (control) => control.formControlName === 'roles'
    );
    if (!rolesControl) {
      return;
    }

    // Check if multi-select should be enabled based on current selection
    var shouldEnableMultiSelect =   this.shouldEnableMultiSelect(selectedRoles);

    // If multi-select is disabled and more than one role is selected, keep only the last selected
    if (!shouldEnableMultiSelect && selectedRoles.length > 1) {
       this.notValidRole();
       const lastSelected = selectedRoles[selectedRoles.length - 1] as  any;
       this.removeRoleFromSelection(lastSelected.id);
    }

  }

  private shouldEnableMultiSelect(selectedRoles: string[]): boolean {
    console.log('shouldEnableMultiSelect called with roles:', selectedRoles);
    var valid = true;
    if (!selectedRoles || selectedRoles.length === 0) {
      valid = false;
    }
    // Normalize role names for comparison
    const normalizedRoles = selectedRoles.map((role) =>
      this.normalizeRoleName(role)
    ).filter(role => role); // Filter out undefined/null values

    // Multi-select is enabled only for exactly 2 roles in valid combinations
    // if (normalizedRoles.length !== 2) {
    //   return false;
    // }
    selectedRoles.forEach((roleName) => {
      const normalizedRole = this.normalizeRoleName(roleName);
      if (this.uniqueRoles.includes(normalizedRole)) {
         valid= false;
      }

    });
    // Valid combinations that enable multi-select:
    // 1. Fund Manager + Board Member
    // 2. Associate Fund Manager + Board Member
    const validCombinations = [
      ['fundmanager', 'boardmember'],
      ['associatedfundmanager', 'boardmember'],
    ];

    if(selectedRoles.length === 2){
       valid = validCombinations.some((combination) => {
       return combination.length === normalizedRoles.length && combination.every((role) => normalizedRoles.includes(role));
    });
  }
  if(selectedRoles.length > 2){
    valid = false;
  }
    // Check if current selection exactly matches any valid combination
    return valid;
  }
  private normalizeRoleName(roleName: any): string {
    if (!roleName) {
      return '';
    }
    let roleNameStr: string;
    if (typeof roleName === 'string') {
      roleNameStr = roleName;
    } else if (typeof roleName === 'object') {
      // Handle role objects - try common property names
      roleNameStr = roleName.id;
    } else {
      roleNameStr = String(roleName);
    }
    // Convert to lowercase for case-insensitive comparison
    const lowerRoleName = roleNameStr.toLowerCase().trim();

    const roleMap: { [key: string]: string } = {
      // Fund Manager variations
      'fundmanager': 'fundmanager',
      // Associate Fund Manager variations
      'associatedfundmanager': 'associatedfundmanager',
      // Board Member variations
      'boardmember': 'boardmember',
      // Other roles
      'legalcouncil': 'legalcouncil',
      'boardsecretary': 'boardsecretary',
      'financecontroller': 'financecontroller',
      'compliancelegalmanagingdirector': 'compliancelegalmanagingdirector',
      'headofrealestate': 'headofrealestate',
    };

    return roleMap[lowerRoleName] || '';
  }
  /**
   * Removes a specific role from the current selection
   */
  private removeRoleFromSelection(roleToRemove: string): void {
    const currentRoles = this.createUserForm.get('roles')?.value || [];
    const updatedRoles = currentRoles.filter(
      (role: string) => role !== roleToRemove
    );
    this.createUserForm.get('roles')?.setValue(updatedRoles);

    // Re-evaluate multi-select settings
    //this.handleRoleChange(updatedRoles);
  }

  /**
   * Checks for role availability conflicts before form submission
   * Returns a Promise that resolves to true if user should proceed, false otherwise
   */
  private async checkRoleAvailabilityConflicts(
    isformSubmission?: boolean
  ): Promise<boolean> {
    const selectedRoles = this.createUserForm.get('roles')?.value || [];

    if (!selectedRoles.length || !this.sigleRolesAvilavilty) {
      return true; // No roles selected or no availability data, proceed
    }

    // Map role names to availability flags
    const roleAvailabilityMap: { [key: string]: string } = {
      legalcouncil: 'legalCouncilHasActiveUser',
      financecontroller: 'financeControllerHasActiveUser',
      compliancelegalmanagingdirector:'complianceLegalManagingDirectorHasActiveUser',
      headofrealestate: 'headOfRealEstateHasActiveUser',
      legalcounciluserfullname: 'legalCouncilUserFullName',
      financecontrolleruserfullname: 'financeControllerUserFullName',
      compliancelegalmanagingdirectoruserfullname:'complianceLegalManagingDirectorUserFullName',
      headofrealestateuserfullname: 'headOfRealEstateUserFullName',
    };

    // Find roles that are already taken
    const conflictingRoles: string[] = [];
    const userNames: string[] = [];
    selectedRoles.forEach((roleName: string) => {
      const normalizedRole = this.normalizeRoleName(roleName);
      const availabilityFlag = roleAvailabilityMap[normalizedRole] || roleAvailabilityMap[roleName];

      if (availabilityFlag && this.sigleRolesAvilavilty[availabilityFlag] === true) {
        conflictingRoles.push(roleName);
        userNames.push(this.sigleRolesAvilavilty[roleAvailabilityMap[normalizedRole + 'userfullname']]);
      }
    });

    // If no conflicts, proceed
    if (conflictingRoles.length === 0) {
      return true;
    }

    // Show confirmation dialog for conflicting roles
    return isformSubmission
      ? this.showRoleAvailabilityConfirmation(conflictingRoles,userNames)
      : false;
  }
  private notValidRole() {

      Swal.fire({
        title: this.translateService.instant(
          'NotValidRole'
        ),
        text: this.translateService.instant(
           'NotValidRole'
        ),
        icon: 'warning',
        showCancelButton: true,
        cancelButtonColor: '#6c757d',

      });

 }
  /**
   * Shows confirmation dialog for roles that are already taken
   */
  private showRoleAvailabilityConfirmation(conflictingRoles: string[] ,userNames: string[]): Promise<boolean> {
    return new Promise((resolve) => {

      const rolesList = conflictingRoles.join(', ');

      Swal.fire({
        title: this.translateService.instant(
          'USER_MANAGEMENT.ROLE_AVAILABILITY.TITLE'
        ),
        text: this.translateService.instant(
          'USER_MANAGEMENT.ROLE_AVAILABILITY.MESSAGE',
          {
            roleName: this.translateService.instant(rolesList),
            userName: userNames,
          }
        ),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: this.translateService.instant(
          'USER_MANAGEMENT.ROLE_AVAILABILITY.CONFIRM'
        ),
        cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
      }).then((result) => {
        resolve(result.isConfirmed);
      });
    });
  }

  // Form submission
  onSubmit() {
    this.isFormSubmitted = true;

    if (this.createUserForm.valid && !this.isLoading) {
      this.isLoading = true;

      // Check for role availability conflicts before proceeding
      this.checkRoleAvailabilityConflicts(true)
        .then((shouldProceed) => {
          if (shouldProceed) {
            // First, handle any pending role replacements
            if (this.pendingRoleReplacements.length > 0) {
              this.processRoleReplacements()
                .then(() => {
                  this.createUser();
                })
                .catch((error) => {
                  console.error('Error processing role replacements:', error);
                  this.isLoading = false;
                });
            } else {
              this.createUser();
            }
          } else {
            this.isLoading = false;
          }
        })
        .catch((error) => {
          console.error('Error checking role availability:', error);
          this.isLoading = false;
        });
    }
  }

  private async processRoleReplacements(): Promise<void> {
    for (const replacement of this.pendingRoleReplacements) {
      try {
        await new Promise<void>((resolve, reject) => {
          this.userManagementService
            .deactivateUser(replacement.existingUserId)
            .subscribe({
              next: () => {
                console.log(
                  `Deactivated user ${replacement.existingUserId} for role ${replacement.roleName}`
                );
                resolve();
              },
              error: (error) => reject(error),
            });
        });
      } catch (error) {
        console.error(
          `Failed to deactivate user for role ${replacement.roleName}:`,
          error
        );
        throw error;
      }
    }
  }

  private createUser(): void {
    const formData = this.createUserForm.value;
    console.log('Creating user with data:', formData);

    // Business Rule 11: Determine if WhatsApp message should be sent
    const roles = Array.isArray(formData.roles)
      ? formData.roles
      : [formData.roles];
    // const hasOnlyBoardMemberRole =
    //   roles.length === 1 && this.normalizeRoleName(roles[0]) === 'boardmember';
    // const registrationMessageIsSent = !hasOnlyBoardMemberRole;

    // Create AddUserCommand from form data
    const addUserCommand: any = {
      email: formData.email,
      iban: formData.iban,
      nationality: formData.nationality,
      passportNo: formData.passportNo,
      fullName: formData.name,
      userName: `${formData.mobile}`, // Saudi mobile number as username
      personalPhoto: formData.personalPhoto,
      roles: roles, // Ensure roles is an array
      // Business Rules 11 & 12: Set flags based on role selection
     // registrationMessageIsSent: registrationMessageIsSent,
    //  registrationIsCompleted: false, // Always false for new users (Business Rule 12)
     // isActive: true, // Business Rule 5: New users default to 'Active' status
      cvFileId : formData.cv ? formData.cv : 0
    };
console.log(addUserCommand)
    this.userManagementService
      .createUser(addUserCommand)
      .pipe(
        catchError((error) => {
          console.error('Error creating user:', error);
          if(error.error.message){
            this.errorModalService.showError(error.error.message);
          }
          else
          this.errorModalService.showError('USER_MANAGEMENT.CREATE.ERROR');
          this.isLoading = false;
          return of(null);
        })
      )
      .subscribe((response) => {
        this.isLoading = false;
        if (response) {

          this.errorModalService.showSuccess(
            response.data ?? 'USER_MANAGEMENT.CREATE.SUCCESS'
          );
          // Clear pending replacements after successful creation
          this.pendingRoleReplacements = [];
          this.router.navigate(['/admin/user-management']);
        }
      });
  }

  onCancel(): void {
    this.router.navigate(['/admin/user-management']);
  }
}
